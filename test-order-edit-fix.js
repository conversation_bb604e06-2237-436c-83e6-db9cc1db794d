// 测试订单修改功能的数据丢失问题修复
// 运行方式: node test-order-edit-fix.js

const orderId = '1'; // 测试订单ID，请根据实际情况修改

// 1. 获取订单详情
async function getOrderDetail() {
  console.log('\n=== 获取订单详情 ===');
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`);
  const data = await response.json();
  
  if (!data.success) {
    console.error('获取订单失败:', data.message);
    return null;
  }
  
  const order = data.data;
  console.log('订单ID:', order.id);
  console.log('订单号:', order.orderNumber);
  console.log('客户信息:', order.customer?.name || '散客');
  console.log('原始备注:', order.note || '无');
  console.log('原始药品数量:', order.items?.length || 0);
  console.log('原始药品清单:', order.items?.map(item => `${item.name} x${item.quantity}`) || []);
  console.log('原始总金额:', order.total);
  console.log('原始优惠金额:', order.discount);
  
  return order;
}

// 2. 测试仅修改备注信息
async function testEditNoteOnly(order) {
  console.log('\n=== 测试仅修改备注信息 ===');
  
  const editData = {
    customerName: order.customer?.name || '',
    customerPhone: order.customer?.phone || '',
    isMember: order.customer?.isMember || false,
    memberNumber: order.customer?.memberNumber || '',
    note: '测试修改备注 - ' + new Date().toLocaleString(),
    discountAmount: order.discount || 0,
    items: order.items || []
  };
  
  console.log('修改前的数据:');
  console.log('- 备注:', order.note || '无');
  console.log('- 药品数量:', order.items?.length || 0);
  console.log('- 药品清单:', order.items?.map(item => `${item.name} x${item.quantity}`) || []);
  
  console.log('\n修改后的数据:');
  console.log('- 备注:', editData.note);
  console.log('- 药品数量:', editData.items.length);
  console.log('- 药品清单:', editData.items.map(item => `${item.name} x${item.quantity}`));
  
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(editData)
  });
  
  const result = await response.json();
  console.log('\n修改结果:', result.success ? '成功' : '失败');
  if (!result.success) {
    console.log('错误信息:', result.message);
  }
  
  return result.success;
}

// 3. 验证修改后的数据
async function verifyAfterEdit() {
  console.log('\n=== 验证修改后的数据 ===');
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`);
  const data = await response.json();
  
  if (!data.success) {
    console.error('获取订单失败:', data.message);
    return false;
  }
  
  const order = data.data;
  
  console.log('验证结果:');
  console.log('- 订单ID:', order.id);
  console.log('- 备注:', order.note || '无');
  console.log('- 药品数量:', order.items?.length || 0);
  console.log('- 药品清单:', order.items?.map(item => `${item.name} x${item.quantity}`) || []);
  console.log('- 总金额:', order.total);
  console.log('- 优惠金额:', order.discount);
  
  const hasItems = order.items && order.items.length > 0;
  console.log('\n数据完整性检查:', hasItems ? '✅ 通过' : '❌ 失败 - 药品清单丢失');
  
  return hasItems;
}

// 4. 测试修改客户信息
async function testEditCustomerInfo(order) {
  console.log('\n=== 测试修改客户信息 ===');
  
  const editData = {
    customerName: '测试客户 - ' + new Date().getTime(),
    customerPhone: '13800138000',
    isMember: true,
    memberNumber: 'VIP' + new Date().getTime(),
    note: order.note || '',
    discountAmount: order.discount || 0,
    items: order.items || []
  };
  
  console.log('修改客户信息:');
  console.log('- 原客户:', order.customer?.name || '散客');
  console.log('- 新客户:', editData.customerName);
  console.log('- 药品数量保持:', editData.items.length);
  
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(editData)
  });
  
  const result = await response.json();
  console.log('修改结果:', result.success ? '成功' : '失败');
  if (!result.success) {
    console.log('错误信息:', result.message);
  }
  
  return result.success;
}

// 5. 测试修改药品数量
async function testEditItemQuantity(order) {
  console.log('\n=== 测试修改药品数量 ===');
  
  if (!order.items || order.items.length === 0) {
    console.log('跳过测试：订单没有药品项目');
    return true;
  }
  
  const editData = {
    customerName: order.customer?.name || '',
    customerPhone: order.customer?.phone || '',
    isMember: order.customer?.isMember || false,
    memberNumber: order.customer?.memberNumber || '',
    note: order.note || '',
    discountAmount: order.discount || 0,
    items: order.items.map((item, index) => ({
      ...item,
      quantity: index === 0 ? item.quantity + 1 : item.quantity, // 修改第一个药品的数量
      subtotal: index === 0 ? item.price * (item.quantity + 1) : item.subtotal
    }))
  };
  
  console.log('修改药品数量:');
  console.log('- 原第一个药品数量:', order.items[0].quantity);
  console.log('- 新第一个药品数量:', editData.items[0].quantity);
  console.log('- 药品总数保持:', editData.items.length);
  
  const response = await fetch(`http://localhost:3000/api/orders/${orderId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(editData)
  });
  
  const result = await response.json();
  console.log('修改结果:', result.success ? '成功' : '失败');
  if (!result.success) {
    console.log('错误信息:', result.message);
  }
  
  return result.success;
}

// 主测试函数
async function runTests() {
  console.log('开始测试订单修改功能的数据丢失问题修复...');
  
  try {
    // 1. 获取原始订单数据
    const originalOrder = await getOrderDetail();
    if (!originalOrder) {
      console.error('无法获取订单数据，测试终止');
      return;
    }
    
    // 2. 测试仅修改备注
    const test1 = await testEditNoteOnly(originalOrder);
    if (test1) {
      await verifyAfterEdit();
    }
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 3. 测试修改客户信息
    const currentOrder = await getOrderDetail();
    if (currentOrder) {
      const test2 = await testEditCustomerInfo(currentOrder);
      if (test2) {
        await verifyAfterEdit();
      }
    }
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 4. 测试修改药品数量
    const currentOrder2 = await getOrderDetail();
    if (currentOrder2) {
      const test3 = await testEditItemQuantity(currentOrder2);
      if (test3) {
        const finalCheck = await verifyAfterEdit();
        
        console.log('\n=== 测试总结 ===');
        console.log('测试1 (仅修改备注):', test1 ? '✅ 通过' : '❌ 失败');
        console.log('测试2 (修改客户信息):', test2 ? '✅ 通过' : '❌ 失败');
        console.log('测试3 (修改药品数量):', test3 ? '✅ 通过' : '❌ 失败');
        console.log('最终数据完整性:', finalCheck ? '✅ 通过' : '❌ 失败');
        
        if (test1 && test2 && test3 && finalCheck) {
          console.log('\n🎉 所有测试通过！数据丢失问题已修复。');
        } else {
          console.log('\n⚠️  部分测试失败，需要进一步检查。');
        }
      }
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
runTests();
